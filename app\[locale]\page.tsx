"use client"

import EvoIndustriesSection from "@/components/EvoIndustriesSection"
import HeroAero3 from "@/components/HeroAero3"
import TechnicalFeatures from "@/components/TechnicalFeatures"
import { Card, CardContent } from "@/components/ui/card"
import { useTranslations } from "next-intl"

export default function Home() {
  const t = useTranslations();
  return (
    <main className="min-h-screen">
      <HeroAero3 />
      <TechnicalFeatures />

      {/* Brand Promise Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h1 className="font-heading text-5xl md:text-7xl mb-6 text-gray-900">{t('home.brandTitle')}</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            {t('home.brandDesc')}
          </p>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-[#009a9f] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-2xl">3</span>
                </div>
                <h3 className="font-heading text-xl mb-2">{t('home.features.0.title')}</h3>
                <p className="text-gray-600">{t('home.features.0.desc')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-[#ff6b00] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-lg">🇩🇿</span>
                </div>
                <h3 className="font-heading text-xl mb-2">{t('home.features.1.title')}</h3>
                <p className="text-gray-600">{t('home.features.1.desc')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-[#e02e8b] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-2xl">★</span>
                </div>
                <h3 className="font-heading text-xl mb-2">{t('home.features.2.title')}</h3>
                <p className="text-gray-600">{t('home.features.2.desc')}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Technologie Triple Lame - Résumé */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-heading text-4xl mb-4">{t('home.tripleBladeTitle')}</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('home.tripleBladeDesc')}
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-heading text-2xl mb-2 color-homme-primary">{t('home.blades.0.title')}</h3>
                <p className="text-gray-600">{t('home.blades.0.desc')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="font-heading text-2xl mb-2 color-femme-primary">{t('home.blades.1.title')}</h3>
                <p className="text-gray-600">{t('home.blades.1.desc')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h3 className="font-heading text-2xl mb-2 color-homme-accent">{t('home.blades.2.title')}</h3>
                <p className="text-gray-600">{t('home.blades.2.desc')}</p>
              </CardContent>
            </Card>
          </div>
          <div className="mt-12 grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-heading text-xl mb-2">{t('home.efficacyTitle')}</h4>
              <p className="text-gray-600 mb-4">{t('home.efficacyDesc')}</p>
              <h4 className="font-heading text-xl mb-2">{t('home.comfortTitle')}</h4>
              <p className="text-gray-600">{t('home.comfortDesc')}</p>
            </div>
            <div>
              <h4 className="font-heading text-xl mb-2">{t('home.precisionTitle')}</h4>
              <p className="text-gray-600 mb-4">{t('home.precisionDesc')}</p>
              <h4 className="font-heading text-xl mb-2">{t('home.durabilityTitle')}</h4>
              <p className="text-gray-600">{t('home.durabilityDesc')}</p>
            </div>
          </div>
        </div>
      </section>

      <EvoIndustriesSection />
    </main>
  )
}
