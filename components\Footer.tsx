import Link from "next/link";
import Image from "next/image";
import {
  Facebook,
  Instagram,
  Linkedin,
  Mail,
  MapPin,
  Phone,
} from "lucide-react";
import { useTranslations } from "next-intl";

export default function Footer() {
  const t = useTranslations();
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-4">
        <div className="grid md:grid-cols-3 gap-8">
          {/* Brand */}
          <div>
            <Image
              src="/logo-shift-expert.svg"
              alt="Shift Expert"
              width={120}
              height={40}
              className="h-8 w-auto mb-4 brightness-0 invert"
            />

            <div className="flex space-x-4 rtl:space-x-reverse">
              <p className="text-gray-400 mb-4">
                {t('footer.brand')}
              </p>
            </div>
            <div className="flex space-x-4 rtl:space-x-reverse">
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Products */}
          <div>
            <h3 className="font-heading text-lg mb-4">{t('footer.products')}</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  {t('footer.aero3')}
                </Link>
              </li>
              <li>
                <Link
                  href="/aero3-pour-elle"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  {t('footer.aero3ForHer')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="font-heading text-lg mb-4">{t('footer.contact')}</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <MapPin className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
                <div className="text-gray-400">
                  <p>{t('footer.address')}</p>
                  <p>{t('footer.city')}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Phone className="w-5 h-5 text-gray-400 flex-shrink-0" />
                <p className="text-gray-400">{t('footer.phone')}</p>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Mail className="w-5 h-5 text-gray-400 flex-shrink-0" />
                <p className="text-gray-400">{t('footer.email')}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-2 pt-2 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            {t('footer.copyright')}
          </p>
          <p className="text-gray-400 text-sm mt-2 md:mt-0">
            {t('footer.madeInAlgeria')}
          </p>
        </div>
      </div>
    </footer>
  );
}
