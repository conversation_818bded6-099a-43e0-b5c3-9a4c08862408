"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";

const navItems = [
  { name: "home", href: "/" },
  { name: "forHim", href: "/aero3-pour-homme" },
  { name: "forHer", href: "/aero3-pour-elle" },
  { name: "investor", href: "/dashboard" },
  { name: "contact", href: "/contact" },
];

const locales = [
  { code: "fr", label: "Français", flag: "🇫🇷" },
  { code: "en", label: "English", flag: "🇺🇸" },
  { code: "ar", label: "العربية", flag: "🇩🇿" },
];

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [langOpen, setLangOpen] = useState(false);
  const t = useTranslations();
  const pathname = usePathname();
  const currentLocale = pathname.split("/")[1] || "fr";
  const currentFlag =
    locales.find((l) => l.code === currentLocale)?.flag || "🇫🇷";

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  // Disable body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-gray-100 backdrop-blur-md border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo Section */}
          <div className="flex items-center space-x-4">
            <Image
              src="/logo-shift-expert.svg"
              alt="Shift Expert"
              width={120}
              height={48}
              className="h-12 w-auto"
              priority
            />


          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-900 hover:text-[#ff6b00] font-medium transition-colors duration-200 relative group"
              >
                {t(`navbar.${item.name}`)}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#ff6b00] transition-all duration-300 group-hover:w-full" />
              </Link>
            ))}
            {/* Language Dropdown */}
            <div className="relative ml-4 rtl:mr-4 rtl:ml-0">
              <button
                className="flex items-center px-3 py-2 rounded-md border border-gray-300 bg-white hover:bg-gray-50 shadow-sm focus:outline-none"
                onClick={() => setLangOpen((v) => !v)}
                aria-label="Select language"
              >
                <span className="text-xl mr-2 rtl:mr-0 rtl:ml-2">{currentFlag}</span>
                <svg
                  className="w-4 h-4 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              {langOpen && (
                <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50 rtl:right-auto rtl:left-0">
                  {locales.map((locale) => (
                    <Link
                      key={locale.code}
                      href={`/${locale.code}${pathname.replace(
                        /^\/[a-z]{2}/,
                        ""
                      )}`}
                      locale={locale.code}
                      className="flex items-center px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-900"
                      onClick={() => setLangOpen(false)}
                    >
                      <span className="text-xl mr-2 rtl:mr-0 rtl:ml-2">{locale.flag}</span>
                      {locale.label}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden p-2 rounded-md hover:bg-gray-100 transition-colors"
            aria-controls="mobile-menu"
            aria-expanded={isOpen}
            aria-label={isOpen ? t("navbar.closeMenu") : t("navbar.openMenu")}
          >
            {isOpen ? (
              <X className="w-6 h-6 text-gray-700" />
            ) : (
              <Menu className="w-6 h-6 text-gray-700" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu Drawer */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40 lg:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeMenu}
            />

            {/* Drawer */}
            <motion.div
              className="fixed top-0 left-0 w-full h-screen bg-white/70 backdrop-blur-md z-50 lg:hidden"
              initial={{ y: "-100%" }}
              animate={{ y: 0 }}
              exit={{ y: "-100%" }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="flex flex-col gap-6 items-start px-6 pt-20 text-gray-900 text-lg font-semibold h-full flex-1 overflow-y-auto">
                {/* Close Button */}
                <button
                  onClick={closeMenu}
                  className="absolute top-6 right-6 p-2 rounded-md hover:bg-gray-100 transition-colors"
                  aria-label={t("navbar.closeMenu")}
                >
                  <X className="w-8 h-8 text-gray-700" />
                </button>

                {/* Mobile Logo Section - Only Shift Expert logo */}
                <div className="w-full flex justify-center items-center mb-8 mt-2">
                  <Link href="/" onClick={closeMenu} className="block">
                    <Image
                      src="/logo-shift-expert.svg"
                      alt="Shift Expert"
                      width={120}
                      height={48}
                      className="h-12 w-auto"
                      priority
                    />
                  </Link>
                </div>

                {/* Navigation Links */}
                <div className="flex flex-col w-full gap-2">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ x: -50, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                    >
                      <Link
                        href={item.href}
                        onClick={closeMenu}
                        className="block py-3 px-2 text-gray-900 hover:text-[#ff6b00] transition-colors duration-200 border-b border-gray-200 w-full"
                      >
                        {t(`navbar.${item.name}`)}
                      </Link>
                    </motion.div>
                  ))}
                </div>

                {/* Mobile Language Dropdown */}
                <motion.div
                  className="w-full"
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: navItems.length * 0.1, duration: 0.3 }}
                >
                  <div className="border-b border-gray-200 pb-3">
                    <div className="text-sm text-gray-600 mb-2 px-2">
                      {t("navbar.language")}
                    </div>
                    <div className="flex flex-col gap-1">
                      {locales.map((locale) => (
                        <Link
                          key={locale.code}
                          href={`/${locale.code}${pathname.replace(
                            /^\/[a-z]{2}/,
                            ""
                          )}`}
                          locale={locale.code}
                          className="flex items-center py-3 px-2 text-gray-900 hover:text-[#ff6b00] transition-colors duration-200"
                          onClick={closeMenu}
                        >
                          <span className="text-xl mr-3 rtl:mr-0 rtl:ml-3">{locale.flag}</span>
                          {locale.label}
                          {locale.code === currentLocale && (
                            <span className="ml-auto rtl:ml-0 rtl:mr-auto text-[#ff6b00] font-medium">
                              ✓
                            </span>
                          )}
                        </Link>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </nav>
  );
}
